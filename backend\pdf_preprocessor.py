"""
PDF Preprocessing Pipeline for Arabic Documents

This module addresses text extraction issues with Arabic PDFs before they reach
the existing FastAPI → Gemini → Mistral workflow. It solves:
- Fragmented text (hundreds of single-character text spans)
- Missing ToUnicode mappings causing character encoding issues
- Glyphs stored in visual form rather than searchable text
- Slow Gemini Flash 2.5 text extraction on problematic documents
- Broken Arabic text: separated letters, reversed reading order, missing characters
"""

import os
import io
import logging
from pathlib import Path
from typing import Optional, Tuple, Dict, List
import tempfile
import shutil

# PDF processing libraries
import fitz  # PyMuPDF
import pdfplumber
from PIL import Image
import pytesseract

# Arabic text processing
import arabic_reshaper
from bidi.algorithm import get_display

# Configuration and error handling
from dotenv import load_dotenv
from preprocessing_config import (
    PreprocessingConfig, DEFAULT_CONFIG,
    PreprocessingError, PDFAnalysisError, TextConsolidationError, OCRError,
    validate_config, get_preprocessing_stats, create_fallback_response,
    log_preprocessing_result
)
import time

load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class PDFPreprocessor:
    """
    PDF preprocessing pipeline for Arabic documents.
    
    Handles text extraction issues by:
    1. Analyzing PDF structure and text quality
    2. Detecting fragmented text spans and encoding issues
    3. Applying OCR-based text layer reconstruction when needed
    4. Consolidating fragmented text spans
    5. Ensuring proper Arabic text encoding and direction
    """
    
    def __init__(self, config: Optional[PreprocessingConfig] = None):
        """
        Initialize the PDF preprocessor.

        Args:
            config: Preprocessing configuration (uses default if None)
        """
        self.config = config or DEFAULT_CONFIG
        validate_config(self.config)

        # Setup Tesseract
        self.tesseract_cmd = self.config.tesseract_cmd or self._find_tesseract()
        if self.tesseract_cmd:
            pytesseract.pytesseract.tesseract_cmd = self.tesseract_cmd

        if self.config.tessdata_dir:
            os.environ['TESSDATA_PREFIX'] = self.config.tessdata_dir
        
    def _find_tesseract(self) -> Optional[str]:
        """Find tesseract executable in common locations."""
        common_paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
            "/usr/bin/tesseract",
            "/usr/local/bin/tesseract",
            "tesseract"  # In PATH
        ]
        
        for path in common_paths:
            if shutil.which(path):
                logger.info(f"Found Tesseract at: {path}")
                return path
        
        logger.warning("Tesseract not found. OCR functionality will be limited.")
        return None
    
    def analyze_pdf_quality(self, pdf_path: str) -> Dict:
        """
        Analyze PDF structure and text quality to determine if preprocessing is needed.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Dictionary with analysis results
        """
        analysis = {
            'needs_preprocessing': False,
            'issues': [],
            'text_spans_count': 0,
            'single_char_spans': 0,
            'fragmentation_ratio': 0.0,
            'has_arabic_text': False,
            'has_searchable_text': False,
            'encoding_issues': False
        }
        
        try:
            # Analyze with PyMuPDF for detailed text span information
            doc = fitz.open(pdf_path)
            total_spans = 0
            single_char_spans = 0
            has_arabic = False
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                text_dict = page.get_text("dict")
                
                for block in text_dict["blocks"]:
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                text = span["text"].strip()
                                total_spans += 1
                                
                                if len(text) == 1:
                                    single_char_spans += 1
                                
                                # Check for Arabic text
                                if self._contains_arabic(text):
                                    has_arabic = True
            
            doc.close()
            
            # Calculate fragmentation ratio
            if total_spans > 0:
                fragmentation_ratio = single_char_spans / total_spans
            else:
                fragmentation_ratio = 0.0

            # Check with pdfplumber for text extraction quality
            with pdfplumber.open(pdf_path) as pdf:
                total_text = ""
                for page in pdf.pages:
                    page_text = page.extract_text() or ""
                    total_text += page_text

                has_searchable_text = len(total_text.strip()) > self.config.min_text_length_threshold
            
            # Update analysis results
            analysis.update({
                'text_spans_count': total_spans,
                'single_char_spans': single_char_spans,
                'fragmentation_ratio': fragmentation_ratio,
                'has_arabic_text': has_arabic,
                'has_searchable_text': has_searchable_text
            })
            
            # Determine if preprocessing is needed
            if fragmentation_ratio > self.config.fragmentation_threshold:
                analysis['issues'].append('High text fragmentation')
                analysis['needs_preprocessing'] = True

            if has_arabic and not has_searchable_text:
                analysis['issues'].append('Arabic text not searchable')
                analysis['needs_preprocessing'] = True

            if has_arabic and fragmentation_ratio > self.config.single_char_ratio_threshold:
                analysis['issues'].append('Arabic text fragmentation')
                analysis['needs_preprocessing'] = True
            
            logger.info(f"PDF analysis complete: {analysis}")
            
        except Exception as e:
            logger.error(f"Error analyzing PDF: {e}")
            analysis['issues'].append(f'Analysis error: {str(e)}')
            raise PDFAnalysisError(f"Failed to analyze PDF: {str(e)}") from e

        return analysis
    
    def _contains_arabic(self, text: str) -> bool:
        """Check if text contains Arabic characters."""
        arabic_range = range(0x0600, 0x06FF + 1)
        return any(ord(char) in arabic_range for char in text)
    
    def preprocess_pdf(self, input_path: str, output_path: str) -> Tuple[bool, Dict]:
        """
        Preprocess PDF to fix text extraction issues.

        Args:
            input_path: Path to input PDF
            output_path: Path to output preprocessed PDF

        Returns:
            Tuple of (success: bool, analysis: Dict)
        """
        start_time = time.time()
        analysis = {}

        try:
            logger.info(f"Starting PDF preprocessing: {input_path}")

            # Analyze PDF quality first
            analysis = self.analyze_pdf_quality(input_path)

            if not analysis['needs_preprocessing']:
                logger.info("PDF doesn't need preprocessing, copying original")
                shutil.copy2(input_path, output_path)
                processing_time = time.time() - start_time
                log_preprocessing_result(True, analysis, processing_time, input_path, output_path)
                return True, analysis

            # Apply appropriate preprocessing based on issues
            success = False
            if 'High text fragmentation' in analysis['issues'] and self.config.enable_text_consolidation:
                success = self._consolidate_text_spans(input_path, output_path)
            elif 'Arabic text not searchable' in analysis['issues'] and self.config.enable_ocr_fallback:
                success = self._apply_ocr_reconstruction(input_path, output_path)
            else:
                # Default: try text consolidation first, then OCR if needed
                if self.config.enable_text_consolidation:
                    success = self._consolidate_text_spans(input_path, output_path)
                if not success and self.config.enable_ocr_fallback:
                    success = self._apply_ocr_reconstruction(input_path, output_path)

            processing_time = time.time() - start_time
            log_preprocessing_result(success, analysis, processing_time, input_path, output_path)
            return success, analysis

        except PreprocessingError as e:
            processing_time = time.time() - start_time
            logger.error(f"Preprocessing error: {e}")
            log_preprocessing_result(False, analysis, processing_time, input_path, output_path)
            return False, analysis
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Unexpected error preprocessing PDF: {e}")
            log_preprocessing_result(False, analysis, processing_time, input_path, output_path)
            return False, analysis
    
    def _consolidate_text_spans(self, input_path: str, output_path: str) -> bool:
        """
        Consolidate fragmented text spans into coherent text.
        
        This method attempts to merge single-character spans into words
        while preserving the original layout and formatting.
        """
        try:
            logger.info("Applying text span consolidation")
            
            doc = fitz.open(input_path)
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Get text blocks and consolidate spans
                text_dict = page.get_text("dict")
                consolidated_blocks = self._consolidate_blocks(text_dict["blocks"])
                
                # Clear existing text and add consolidated text
                # Note: This is a simplified approach. In practice, you might need
                # more sophisticated text positioning and formatting preservation
                page.clean_contents()
                
                for block in consolidated_blocks:
                    if block.get('consolidated_text'):
                        # Add consolidated text back to the page
                        # This is a basic implementation - real implementation would
                        # need to preserve exact positioning, fonts, and formatting
                        rect = fitz.Rect(block['bbox'])
                        page.insert_textbox(rect, block['consolidated_text'])
            
            doc.save(output_path)
            doc.close()
            
            logger.info(f"Text consolidation complete: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error in text consolidation: {e}")
            return False
    
    def _consolidate_blocks(self, blocks: List[Dict]) -> List[Dict]:
        """Consolidate text spans within blocks."""
        consolidated = []
        
        for block in blocks:
            if "lines" in block:
                consolidated_text = ""
                for line in block["lines"]:
                    line_text = ""
                    for span in line["spans"]:
                        text = span["text"]
                        if self._contains_arabic(text):
                            # Apply Arabic text processing
                            text = self._process_arabic_text(text)
                        line_text += text
                    consolidated_text += line_text + "\n"
                
                block['consolidated_text'] = consolidated_text.strip()
                consolidated.append(block)
        
        return consolidated
    
    def _process_arabic_text(self, text: str) -> str:
        """Process Arabic text for proper display and connection."""
        try:
            # Reshape Arabic text for proper character connections
            reshaped_text = arabic_reshaper.reshape(text)
            # Apply bidirectional algorithm for proper RTL display
            display_text = get_display(reshaped_text)
            return display_text
        except Exception as e:
            logger.warning(f"Error processing Arabic text: {e}")
            return text
    
    def _apply_ocr_reconstruction(self, input_path: str, output_path: str) -> bool:
        """
        Apply OCR-based text layer reconstruction.
        
        This method converts PDF pages to images, applies OCR with Arabic support,
        and creates a new PDF with a proper searchable text layer.
        """
        if not self.tesseract_cmd:
            logger.error("Tesseract not available for OCR reconstruction")
            return False
        
        try:
            logger.info("Applying OCR-based text layer reconstruction")
            
            doc = fitz.open(input_path)
            new_doc = fitz.open()  # Create new document
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Convert page to image
                mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better OCR
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # Apply OCR with Arabic support
                image = Image.open(io.BytesIO(img_data))

                # Resize image if too large
                if image.size[0] > self.config.max_image_size[0] or image.size[1] > self.config.max_image_size[1]:
                    image.thumbnail(self.config.max_image_size, Image.Resampling.LANCZOS)

                ocr_config = f'--psm {self.config.ocr_psm} --dpi {self.config.ocr_dpi}'
                ocr_text = pytesseract.image_to_string(
                    image,
                    lang=self.config.ocr_languages,
                    config=ocr_config
                )
                
                # Process Arabic text
                if self._contains_arabic(ocr_text):
                    ocr_text = self._process_arabic_text(ocr_text)
                
                # Create new page with OCR text
                new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
                
                # Insert original image as background
                new_page.insert_image(new_page.rect, pixmap=pix)
                
                # Add invisible text layer for searchability
                if ocr_text.strip():
                    new_page.insert_textbox(
                        new_page.rect, 
                        ocr_text,
                        fontsize=1,  # Very small, invisible text
                        color=(1, 1, 1),  # White text (invisible on white background)
                        overlay=False
                    )
            
            new_doc.save(output_path)
            new_doc.close()
            doc.close()
            
            logger.info(f"OCR reconstruction complete: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error in OCR reconstruction: {e}")
            return False


def preprocess_arabic_pdf(input_path: str, output_path: str,
                         config: Optional[PreprocessingConfig] = None) -> Tuple[bool, Dict]:
    """
    Main function to preprocess Arabic PDFs.

    Args:
        input_path: Path to input PDF
        output_path: Path to output preprocessed PDF
        config: Optional preprocessing configuration

    Returns:
        Tuple of (success: bool, analysis: Dict)
    """
    try:
        preprocessor = PDFPreprocessor(config)
        return preprocessor.preprocess_pdf(input_path, output_path)
    except Exception as e:
        logger.error(f"Failed to initialize preprocessor: {e}")
        return False, create_fallback_response(e, input_path)


if __name__ == "__main__":
    import sys
    if len(sys.argv) != 3:
        print("Usage: python pdf_preprocessor.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    success, analysis = preprocess_arabic_pdf(input_file, output_file)
    
    if success:
        print(f"✅ Preprocessing successful: {output_file}")
        print(f"📊 Analysis: {analysis}")
    else:
        print("❌ Preprocessing failed")
        sys.exit(1)
