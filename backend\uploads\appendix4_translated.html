<!DOCTYPE html>
<html lang='en'>
<head>
<meta charset='UTF-8'>
<meta name='viewport' content='width=device-width, initial-scale=1.0'>
<title>Translated Document</title>
<style>

        body {
            font-family: 'Segoe UI', Arial, Helvetica, Tahoma, Geneva, Verdana, sans-serif;
            font-size: 17px;
            line-height: 1.8;
            color: #222;
            margin: 0;
            padding: 0;
            background: #f8fafd;
            direction: ltr;
        }
        .document-container {
            padding: 48px 32px 48px 32px;
            max-width: 900px;
            margin: 40px auto 40px auto;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.08);
        }
        h1 {
            font-size: 2.8em;
            font-weight: 700;
            color: #003366;
            text-align: center;
            margin-top: 0;
            margin-bottom: 0.7em;
            letter-spacing: 0.02em;
        }
        h2 {
            font-size: 1.7em;
            color: #003366;
            font-weight: 600;
            margin-top: 2.5em;
            margin-bottom: 0.7em;
            border-bottom: 2px solid #e0e6ed;
            padding-bottom: 6px;
            letter-spacing: 0.01em;
        }
        h3 {
            font-size: 1.25em;
            color: #00509e;
            font-weight: 600;
            margin-top: 2em;
            margin-bottom: 0.5em;
        }
        h4, h5, h6 {
            font-size: 1.1em;
            color: #003366;
            font-weight: 500;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        p {
            margin: 18px 0;
            color: #333;
            text-align: justify;
            text-justify: inter-word;
        }
        ul, ol {
            margin: 18px 0 18px 36px;
            padding-left: 1.2em;
        }
        ul {
            list-style-type: disc;
        }
        ol {
            list-style-type: decimal;
        }
        ul ul, ol ul {
            list-style-type: circle;
            margin-top: 0;
            margin-bottom: 0;
        }
        ul ol, ol ol {
            list-style-type: lower-latin;
            margin-top: 0;
            margin-bottom: 0;
        }
        li {
            margin-bottom: 0.4em;
            font-size: 1em;
        }
        /* Table container with horizontal scroll for wide tables */
        .table-container {
            width: 100%;
            overflow-x: auto;
            margin: 32px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
            background: #fff;
        }

        table {
            width: 100%;
            min-width: 800px; /* Minimum width to prevent cramping */
            border-collapse: collapse;
            margin: 0;
            background: #fff;
        }

        /* Responsive table sizing based on column count */
        table.wide-table {
            min-width: 1200px; /* For tables with 8+ columns */
        }

        table.extra-wide-table {
            min-width: 1600px; /* For tables with 11+ columns */
        }

        table th, table td {
            border: 1px solid #d1dbe6;
            padding: 12px 8px; /* Reduced padding for wide tables */
            text-align: left;
            vertical-align: top;
            white-space: nowrap; /* Prevent text wrapping in headers */
            min-width: 80px; /* Minimum column width */
        }

        /* Allow text wrapping in description columns */
        table td:nth-child(5), /* Item Description */
        table td:nth-child(6)  /* Specifications */ {
            white-space: normal;
            max-width: 200px;
            min-width: 150px;
        }

        table th {
            background-color: #003366;
            color: #fff;
            font-weight: 700;
            font-size: 1em; /* Slightly smaller for wide tables */
            position: sticky;
            top: 0;
            z-index: 10;
        }

        table tr:nth-child(even) td {
            background-color: #f4f8fb;
        }

        table tr:hover td {
            background-color: #e6f0fa;
        }
        @media (max-width: 1200px) {
            .document-container {
                max-width: 95vw; /* Allow wider container for large tables */
            }
        }

        @media (max-width: 700px) {
            .document-container {
                padding: 16px 4px;
                max-width: 100vw;
            }
            h1 { font-size: 2em; }
            h2 { font-size: 1.2em; }

            /* Mobile table adjustments */
            table th, table td {
                padding: 8px 4px;
                font-size: 0.9em;
            }

            table th {
                font-size: 0.85em;
            }

            /* Reduce minimum widths on mobile */
            table {
                min-width: 600px;
            }

            table.wide-table {
                min-width: 900px;
            }

            table.extra-wide-table {
                min-width: 1200px;
            }
        }
        @media print {
            body, .document-container {
                background: #fff !important;
                box-shadow: none !important;
            }
            .document-container {
                padding: 0;
                margin: 0;
            }
        }
        
</style>
</head>
<body>
<div class='document-container'>
<h1>Appendix No. 4</h1>
<h2>Special Conditions:</h2>
<ol>
<li>Classification:</li>
</ol>
<p>The service provider is committed to providing the required contractor classification certificate for the project, specifying the field, activity, and code of the project, and it should be among the following classifications:</p>
<div class="table-container"><table>
<thead>
<tr>
<th style="text-align:left">Code</th>
<th style="text-align:left">Activity Name</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">631121</td>
<td style="text-align:left">Establishing the infrastructure for hosting websites, data processing services, and related activities</td>
</tr>
<tr>
<td style="text-align:left">620111</td>
<td style="text-align:left">Application Development</td>
</tr>
<tr>
<td style="text-align:left">631125</td>
<td style="text-align:left">Registration for providing cloud computing services</td>
</tr>
</tbody>
</table></div>
<ol start="2">
<li>The service provider is committed to providing the necessary safety conditions and precautions for operation, in accordance with the rules in effect in the Kingdom, global standards, and university instructions.</li>
<li>The service provider must adhere to the regulations and systems of King Abdulaziz University and Saudi universities.</li>
<li>The service provider is committed to providing the necessary technical support.</li>
<li>The service provider is committed to transferring expertise to the admin and providing technical assistance when necessary.</li>
<li>The cloud service provider must have worked in the public cloud sector for a minimum of 5 years.</li>
<li>The cloud service provider must have a record of reducing the prices of the services provided.</li>
<li>The number of times prices have been reduced since the start of operations in the massive commercial cloud sector must be specified.</li>
<li>The cloud service provider must have an online e-marketplace that allows customers to access third-party applications and services to enhance the provider's offerings.</li>
<li>The cloud service provider must support the approved Ellucian architecture.</li>
<li>The cloud service provider must provide memory-optimized instances designed for memory-intensive applications.</li>
<li>The cloud service provider must provide memory capacity increases without increasing the number of vCPU units.</li>
<li>The cloud service provider must provide compute-optimized instances designed for processing-intensive applications.</li>
<li>The cloud service provider must provide increases in the number of VCPU units without increasing memory.</li>
<li>The cloud service provider must provide storage-optimized instances that provide large local storage capacity.</li>
<li>The cloud service provider must provide storage capacity instances without increasing the number of VCPU units or memory.</li>
<li>The cloud service provider must provide burstable instances that provide a basic level of CPU performance with the ability to burst above the basic level.</li>
<li>The cloud service provider must provide instances that use NVMe SSD drives optimized for low latency, very high I/O performance, and high sequential read.</li>
<li>The cloud service provider must provide Bare Metal instances that run on hardware dedicated to a single user only.</li>
<li>The cloud service provider must allow users to logically aggregate instances within the same data center.</li>
<li>The cloud service provider must allow users to logically aggregate instances and place them in different data centers within the same region.</li>
<li>The cloud service provider must provide the ability to provision multiple instances simultaneously via an API, administrative console, or web portal.</li>
<li>The cloud service provider must provide dedicated instances with the ability to modify configuration settings such as virtual CPUs (VCPUs).</li>
<li>The cloud service provider must provide dedicated instances with the ability to modify both virtual CPUs (vCPUs) and RAM.</li>
<li>The cloud service provider must provide a way to provision instances to support Intel's Software Guard Extensions (SGX) technology for encrypting data in use.</li>
<li>The cloud service provider must support role-based access control (RBAC).</li>
<li>The cloud service provider must allow users to reset their passwords in a self-service manner.</li>
<li>The cloud service provider must provide the ability to add permissions for users and groups at the resource level.</li>
<li>The cloud service provider must provide the ability to create permissions that are valid for a specific period of time.</li>
<li>The cloud service provider's architecture must contain built-in access control policies that can be linked to users and groups.</li>
<li>The cloud service provider's architecture must allow the creation and customization of access control policies that can be linked to users and groups.</li>
<li>The cloud service provider must provide a mechanism to test the impact of access control policies before applying them in the production environment.</li>
<li>The cloud service provider must support the ability to whitelist IP addresses through the Identity and Access Management (IAM) service.</li>
<li>The cloud service provider must support multi-factor authentication (MFA) as an additional layer of access control and authentication.</li>
<li>The cloud service provider must provide the ability to create and link dynamic, changeable tags to improve management.</li>
<li>The cloud service provider must offer a Service Level Agreement (SLA) for the Identity and Access Management service.</li>
<li>All employees of the cloud service provider who have access to the infrastructure (whether physical or non-physical) must undergo security background checks.</li>
<li>The cloud service provider must prevent service provider employees and their equivalents from accessing the service infrastructure unless there is a support ticket, change request, or similar official authorization.</li>
<li>The cloud service provider must log all access to its infrastructure, retaining logs for at least 90 days.</li>
<li>The cloud service provider must prevent employees from logging into computer servers, and all tasks must be performed automatically, with the contents of these automated operations being logged and retained for at least 90 days.</li>
<li>The cloud service provider must provide a service for creating and controlling cryptographic keys to encrypt user data.</li>
<li>The cloud service provider must allow users to import their own keys from their key management systems into the cloud key management service.</li>
<li>The cloud service provider's key management service must integrate with other cloud services to provide data encryption during storage.</li>
<li>The cloud service provider must provide dedicated Hardware Security Modules (HSMs) that offer secure key storage and tamper-resistant encryption operations.</li>
<li>The cloud service provider must support key continuity, including storing multiple copies to ensure availability when needed.</li>
<li>The cloud service provider must provide a managed Single Sign-On (SSO) service that allows users to centrally manage access to accounts and applications.</li>
<li>The cloud service provider must provide a managed service for issuing, managing, and deploying SSL/TLS certificates.</li>
<li>The cloud service provider's certificate management service must facilitate the certificate renewal process.</li>
<li>The cloud service provider's certificate management service must support the use of Wildcard certificates.</li>
<li>The cloud service provider's certificate management service must act as a Certificate Authority (CA).</li>
<li>The cloud service provider must provide a managed Microsoft Active Directory service in the cloud.</li>
<li>The managed Microsoft Active Directory service in the cloud must support integration with on-premises Active Directory (AD).</li>
<li>The managed Microsoft Active Directory service must support the LDAP protocol.</li>
<li>The managed Microsoft Active Directory service must support the SAML protocol.</li>
<li>The cloud service provider must provide a managed service for rotating, managing, and retrieving credentials such as API keys, database passwords, and other secrets.</li>
<li>The cloud service provider must provide a Web Application Firewall (WAF) to protect applications from common attacks that affect availability and security.</li>
<li>The cloud service provider must provide a service to protect against DDoS attacks at the network and transport levels, with the ability to write custom rules to mitigate advanced attacks at the application level, including layer 7.</li>
<li>The cloud service provider must provide a service to automatically assess vulnerabilities in applications and resources.</li>
<li>The cloud service provider must provide a managed service for threat detection.</li>
<li>The cloud service provider must provide a managed service for aggregating security results and enabling users to centrally view and manage security alerts.</li>
<li>The cloud service provider must support vulnerability management through an automated process that maintains the security of the cloud environment from cyber-attacks and data leaks.</li>
<li>The cloud service provider must conduct periodic scans to detect security vulnerabilities.</li>
<li>The cloud service provider must allow third parties to conduct periodic penetration tests and vulnerability scans on the users' applications and networks.</li>
<li>The cloud service provider must provide tools for managing user identities and controlling access to resources.</li>
<li>The cloud service provider must provide a Data Loss Prevention (DLP) solution or data extraction prevention for all systems that integrate with the cloud service.</li>
<li>The cloud service provider must have cybersecurity standards for each component of its infrastructure, such as virtual machines, operating systems, routers, DNS servers, and others.</li>
<li>The service provider acknowledges and commits to not imposing any additional fees or costs on the university in the event of a desire to switch to another service provider or decide to return to the main campus, and this must be done unconditionally and at any time.</li>
<li>The service provider is committed to transferring the unused balance to the following years without requiring a minimum annual usage limit.</li>
<li>The contract duration is (3) years from the date of site receipt.</li>
<li>Payments:</li>
</ol>
<p>Payments are made in two installments as follows:</p>
<h3>First Installment:</h3>
<ul>
<li>The value of items (1 to 37) will be paid 4 months after the date of site receipt.</li>
</ul>
<h3>Second Installment:</h3>
<ul>
<li>The value of item number (38) will be paid 3 years after the date of the first installment payment.</li>
</ul>
<ol start="71">
<li>Penalties:</li>
</ol>
<p>A performance penalty will be imposed on the service provider at a rate not exceeding (1% to 20%) of the invoice value if they fail to provide the required services.</p>
</div>
</body>
</html>