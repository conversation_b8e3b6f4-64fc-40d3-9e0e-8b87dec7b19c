import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  backTo?: string;
  children?: React.ReactNode;
}

const Header: React.FC<HeaderProps> = ({ 
  title, 
  showBackButton = false, 
  backTo = '/',
  children 
}) => {
  const navigate = useNavigate();

  return (
    <div className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-10">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between h-12">
          <div className="flex items-center gap-6 h-full">
            {/* Logo */}
            <div className="flex items-center justify-center h-full">
              <img
                src="/f3.svg"
                alt="F3 Logo"
                className="h-8 w-auto cursor-pointer transition-all duration-200 hover:opacity-80"
                onClick={() => navigate('/')}
              />
            </div>

            {/* Back Button */}
            {showBackButton && (
              <>
                <div className="flex items-center h-full">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate(backTo)}
                    className="text-slate-400 hover:text-white transition-all duration-200 h-8 px-3"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                  </Button>
                </div>
                <div className="h-6 border-l border-slate-600"></div>
              </>
            )}

            {/* Title */}
            <div className="flex items-center h-full">
              <h1 className="text-xl font-semibold text-slate-200 leading-none">
                {title}
              </h1>
            </div>
          </div>

          {/* Right side content */}
          <div className="flex items-center gap-3">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header; 