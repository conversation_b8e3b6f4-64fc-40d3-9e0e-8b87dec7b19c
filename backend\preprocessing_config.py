"""
Configuration and utilities for PDF preprocessing pipeline.

This module provides configuration options, error handling, and utility functions
for the Arabic PDF preprocessing pipeline.
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

# Configure logging
def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """Setup logging configuration for preprocessing pipeline."""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Setup root logger
    logger = logging.getLogger()
    logger.setLevel(level)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (optional)
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

@dataclass
class PreprocessingConfig:
    """Configuration class for PDF preprocessing parameters."""
    
    # Text analysis thresholds
    min_text_length_threshold: int = 10
    fragmentation_threshold: float = 0.7
    single_char_ratio_threshold: float = 0.5
    
    # OCR configuration
    ocr_confidence_threshold: int = 60
    ocr_dpi: int = 300
    ocr_languages: str = "ara+eng"
    ocr_psm: int = 6  # Assume uniform block of text
    
    # Tesseract paths (will be auto-detected if not specified)
    tesseract_cmd: Optional[str] = None
    tessdata_dir: Optional[str] = None
    
    # Processing options
    enable_text_consolidation: bool = True
    enable_ocr_fallback: bool = True
    preserve_original_layout: bool = True
    apply_arabic_reshaping: bool = True
    
    # Performance settings
    max_image_size: tuple = (3000, 3000)  # Max image dimensions for OCR
    processing_timeout: int = 300  # 5 minutes timeout
    
    # Output options
    save_intermediate_files: bool = False
    cleanup_temp_files: bool = True
    
    @classmethod
    def from_env(cls) -> 'PreprocessingConfig':
        """Create configuration from environment variables."""
        return cls(
            min_text_length_threshold=int(os.getenv('PDF_MIN_TEXT_LENGTH', '10')),
            fragmentation_threshold=float(os.getenv('PDF_FRAGMENTATION_THRESHOLD', '0.7')),
            single_char_ratio_threshold=float(os.getenv('PDF_SINGLE_CHAR_RATIO', '0.5')),
            ocr_confidence_threshold=int(os.getenv('OCR_CONFIDENCE_THRESHOLD', '60')),
            ocr_dpi=int(os.getenv('OCR_DPI', '300')),
            ocr_languages=os.getenv('OCR_LANGUAGES', 'ara+eng'),
            ocr_psm=int(os.getenv('OCR_PSM', '6')),
            tesseract_cmd=os.getenv('TESSERACT_CMD'),
            tessdata_dir=os.getenv('TESSDATA_DIR'),
            enable_text_consolidation=os.getenv('ENABLE_TEXT_CONSOLIDATION', 'true').lower() == 'true',
            enable_ocr_fallback=os.getenv('ENABLE_OCR_FALLBACK', 'true').lower() == 'true',
            preserve_original_layout=os.getenv('PRESERVE_ORIGINAL_LAYOUT', 'true').lower() == 'true',
            apply_arabic_reshaping=os.getenv('APPLY_ARABIC_RESHAPING', 'true').lower() == 'true',
            max_image_size=tuple(map(int, os.getenv('MAX_IMAGE_SIZE', '3000,3000').split(','))),
            processing_timeout=int(os.getenv('PROCESSING_TIMEOUT', '300')),
            save_intermediate_files=os.getenv('SAVE_INTERMEDIATE_FILES', 'false').lower() == 'true',
            cleanup_temp_files=os.getenv('CLEANUP_TEMP_FILES', 'true').lower() == 'true'
        )

class PreprocessingError(Exception):
    """Base exception for preprocessing errors."""
    pass

class PDFAnalysisError(PreprocessingError):
    """Exception raised when PDF analysis fails."""
    pass

class TextConsolidationError(PreprocessingError):
    """Exception raised when text consolidation fails."""
    pass

class OCRError(PreprocessingError):
    """Exception raised when OCR processing fails."""
    pass

class ConfigurationError(PreprocessingError):
    """Exception raised when configuration is invalid."""
    pass

def validate_config(config: PreprocessingConfig) -> None:
    """Validate preprocessing configuration."""
    if config.fragmentation_threshold < 0 or config.fragmentation_threshold > 1:
        raise ConfigurationError("fragmentation_threshold must be between 0 and 1")
    
    if config.single_char_ratio_threshold < 0 or config.single_char_ratio_threshold > 1:
        raise ConfigurationError("single_char_ratio_threshold must be between 0 and 1")
    
    if config.ocr_confidence_threshold < 0 or config.ocr_confidence_threshold > 100:
        raise ConfigurationError("ocr_confidence_threshold must be between 0 and 100")
    
    if config.processing_timeout <= 0:
        raise ConfigurationError("processing_timeout must be positive")

def get_preprocessing_stats(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """Generate preprocessing statistics from analysis results."""
    stats = {
        'total_text_spans': analysis.get('text_spans_count', 0),
        'single_char_spans': analysis.get('single_char_spans', 0),
        'fragmentation_ratio': analysis.get('fragmentation_ratio', 0.0),
        'has_arabic_text': analysis.get('has_arabic_text', False),
        'has_searchable_text': analysis.get('has_searchable_text', False),
        'preprocessing_needed': analysis.get('needs_preprocessing', False),
        'issues_detected': len(analysis.get('issues', [])),
        'issues': analysis.get('issues', [])
    }
    
    # Add quality assessment
    if stats['fragmentation_ratio'] > 0.8:
        stats['text_quality'] = 'poor'
    elif stats['fragmentation_ratio'] > 0.5:
        stats['text_quality'] = 'fair'
    else:
        stats['text_quality'] = 'good'
    
    return stats

def create_fallback_response(error: Exception, original_path: str) -> Dict[str, Any]:
    """Create a fallback response when preprocessing fails."""
    return {
        'success': False,
        'error': str(error),
        'error_type': type(error).__name__,
        'fallback_action': 'using_original_pdf',
        'original_path': original_path,
        'message': f"Preprocessing failed ({type(error).__name__}), using original PDF"
    }

def log_preprocessing_result(success: bool, analysis: Dict[str, Any], 
                           processing_time: float, input_path: str, 
                           output_path: str) -> None:
    """Log preprocessing results with detailed information."""
    logger = logging.getLogger(__name__)
    
    stats = get_preprocessing_stats(analysis)
    
    if success:
        logger.info(
            f"PDF preprocessing completed successfully in {processing_time:.2f}s\n"
            f"  Input: {input_path}\n"
            f"  Output: {output_path}\n"
            f"  Text Quality: {stats['text_quality']}\n"
            f"  Fragmentation Ratio: {stats['fragmentation_ratio']:.3f}\n"
            f"  Issues Detected: {stats['issues_detected']}\n"
            f"  Arabic Text: {stats['has_arabic_text']}\n"
            f"  Preprocessing Needed: {stats['preprocessing_needed']}"
        )
    else:
        logger.error(
            f"PDF preprocessing failed after {processing_time:.2f}s\n"
            f"  Input: {input_path}\n"
            f"  Issues: {', '.join(stats['issues'])}"
        )

# Default configuration instance
DEFAULT_CONFIG = PreprocessingConfig.from_env()

# Setup logging on module import
setup_logging(
    log_level=os.getenv('LOG_LEVEL', 'INFO'),
    log_file=os.getenv('LOG_FILE')
)
