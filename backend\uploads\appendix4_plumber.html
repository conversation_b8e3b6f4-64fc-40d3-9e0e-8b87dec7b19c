
    <!DOCTYPE html>
    <html lang='ar' dir='rtl'>
    <head>
    <meta charset='UTF-8'>
    <title>Translated Arabic PDF</title>
    <style>
    body { font-family: Tahoma, sans-serif; font-size: 16px; direction: ltr; padding: 40px; max-width: 1000px; margin: auto; }
    table { width: 100%; border-collapse: collapse; margin: 24px 0; background: #fff; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    th, td { border: 1px solid #ccc; padding: 12px; text-align: left; }
    th { background: #003366; color: white; }
    tr:nth-child(even) { background-color: #f2f2f2; }
    </style>
    </head>
    <body>
    <p>ملحق رقم 4</p>
<h2>الشروط الخاصة :</h2>
<ol>
<li>التصنيف:</li>
</ol>
<p>يلتزم مزود الخدمة بتقديم شهادة تصنيف المقاولين المطلوبة للمشروع مع تحديد مجال ونشاط المشروع ورمزه وأن يكون من ضمن التصنيفات التالية:</p>
<table>
<thead>
<tr>
<th style="text-align:left">الرمز</th>
<th style="text-align:left">اسم النشاط</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">631121</td>
<td style="text-align:left">إقامة البنية الأساسية لاستضافة المواقع على الشبكة وخدمات تجهيز البيانات والأنشطة المتصلة بذلك</td>
</tr>
<tr>
<td style="text-align:left">620111</td>
<td style="text-align:left">تطوير التطبيقات</td>
</tr>
<tr>
<td style="text-align:left">631125</td>
<td style="text-align:left">التسجيل لتقديم خدمات الحوسبة السحابية</td>
</tr>
</tbody>
</table>
<ol start="2">
<li>يلتزم مزود الخدمة بتوفير شروط واحتياطات الأمان الضرورية بالتشغيل وذلك حسب القواعد المعمول بها في المملكة والمعايير العالمية وتعليمات الجامعة.</li>
<li>على مزود الخدمة ضرورة التقيد باللوائح والأنظمة الخاصة بجامعة الملك عبد العزيز والجامعات السعودية.</li>
<li>يلتزم مزود الخدمة بتقديم الدعم الفني اللازم.</li>
<li>يلتزم مزود الخدمة بنقل الخبرة للادمين وتقديم المساعدة التقنية عند الضرورة.</li>
<li>يجب أن يكون مزود الخدمة السحابية قد عمل في قطاع السحابة العامة لمدة 5 سنوات كحد ادنى.</li>
<li>يجب أن يكون لمزود الخدمة السحابية سجل في خفض أسعار الخدمات المقدمة.</li>
<li>يجب تحديد عدد مرات تخفيض الأسعار منذ بدء التشغيل في قطاع السحابة التجارية الضخمة.</li>
<li>يجب أن يكون لدى مزود الخدمة السحابية سوق إلكتروني عبر الإنترنت يتيح للعملاء الوصول إلى تطبيقات وخدمات الجهات الخارجية لتعزيز عروض المزود.</li>
<li>يجب أن يدعم مزود الخدمة السحابية بنية Ellucian المعتمدة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية حالات محسنة للذاكرة – مصممة للتطبيقات كثيفة الاستخدام للذاكرة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية زيادة سعة الذاكرة دون زيادة عدد وحدات vCPU.</li>
<li>يجب أن يوفر مزود الخدمة السحابية حالات محسنة للحوسبة - مصممة للتطبيقات كثيفة الاستخدام للمعالجة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية زيادة عدد وحدات VCPU دون زيادة الذاكرة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية حالات محسنة للتخزين - توفر سعة تخزين محلية كبيرة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية حالات سعة التخزين دون زيادة عدد وحدات VCPU أو الذاكرة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية حالات قابلة للاندفاع (Burstable) توفر مستوى أساسيًا من أداء وحدة المعالجة المركزية (CPU) مع إمكانية الاندفاع فوق المستوى الأساسي.</li>
<li>يجب أن يوفر مزود الخدمة السحابية حالات تستخدم محركات أقراص NVMe SSD المحسنة لزمن استجابة منخفض وأداء إدخال / إخراج (I/O) عال جدًا وقراءة تسلسلية عالية.</li>
<li>يجب أن يوفر مزود الخدمة السحابية حالات Bare Metal تعمل على أجهزة مخصصة لمستخدم واحد فقط.</li>
<li>يجب أن يوفر مزود الخدمة السحابية للمستخدمين إمكانية تجميع الحالات منطقيًا ضمن نفس مركز البيانات.</li>
<li>يجب أن يوفر مزود الخدمة السحابية للمستخدمين إمكانية تجميع الحالات منطقيًا ووضعها في مراكز بيانات مختلفة ضمن نفس المنطقة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية إمكانية توفير ذاتي لعدة حالات بشكل متزامن عبر واجهة برمجية، أو وحدة تحكم إدارية، أو بوابة ويب.</li>
<li>يجب أن يوفر مزود الخدمة السحابية حالات مخصصة مع القدرة على تعديل إعدادات التكوين مثل وحدات المعالجة المركزية الافتراضية (VCPUs).</li>
<li>يجب أن يوفر مزود الخدمة السحابية حالات مخصصة مع القدرة على تعديل كل من وحدات المعالجة المركزية الافتراضية (vCPUs) وذاكرة الوصول العشوائي (RAM).</li>
<li>يجب أن يوفر مزود الخدمة السحابية طريقة لتخصيص الحالات لدعم تقنية Intel (Software Guard Extensions (SGX لتشفير البيانات أثناء الاستخدام.</li>
<li>يجب أن يدعم مزود الخدمة السحابية التحكم في الوصول المستند إلى الأدوار. (RBAC)</li>
<li>يجب أن يسمح مزود الخدمة السحابية للمستخدمين بإعادة تعيين كلمات المرور الخاصة بهم بطريقة ذاتية الخدمة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية القدرة على إضافة أذونات للمستخدمين والمجموعات على مستوى المورد.</li>
<li>يجب أن يوفر مزود الخدمة السحابية القدرة على إنشاء أذونات تكون صالحة لفترة زمنية محددة.</li>
<li>يجب أن تحتوي بنية مزود الخدمة السحابية على سياسات تحكم في الوصول مدمجة يمكن ربطها بالمستخدمين والمجموعات.</li>
<li>يجب أن تسمح بنية مزود الخدمة السحابية بإنشاء وتخصيص سياسات تحكم في الوصول يمكن ربطها بالمستخدمين والمجموعات.</li>
<li>يجب أن يوفر مزود الخدمة السحابية آلية لاختبار تأثير سياسات التحكم في الوصول قبل تطبيقها في بيئة الإنتاج.</li>
<li>يجب أن يدعم مزود الخدمة السحابية إمكانية إدراج عناوين IP في القائمة البيضاء من خلال خدمة إدارة الهوية والوصول. (IAM)</li>
<li>يجب أن يدعم مزود الخدمة السحابية المصادقة متعددة العوامل (MFA) كطبقة إضافية من التحكم في الوصول والمصادقة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية القدرة على إنشاء وربط علامات ديناميكية قابلة للتغيير لتحسين الإدارة.</li>
<li>يجب أن يقدم مزود الخدمة السحابية اتفاقية مستوى خدمة (SLA) لخدمة إدارة الهوية والوصول.</li>
<li>يجب أن يخضع جميع موظفي مزود الخدمة السحابية الذين لديهم وصول إلى البنية التحتية (سواء كانت مادية أو غير مادية) لفحوصات خلفية أمنية.</li>
<li>يجب أن يمنع مزود الخدمة السحابية موظفي مزود الخدمة ومن في حكمهم من الوصول إلى البنية التحتية للخدمة إلا عند وجود تذكرة دعم، أو طلب تغيير، أو تفويض رسمي مماثل.</li>
<li>يجب أن يسجل مزود الخدمة السحابية جميع عمليات الوصول إلى بنيته التحتية، مع الاحتفاظ بالسجلات لمدة لا تقل عن 90 يوما.</li>
<li>يجب أن يمنع مزود الخدمة السحابية الموظفين من تسجيل الدخول إلى الخوادم الحاسوبية، ويجب أن تتم جميع المهام بطريقة آلية مع تسجيل محتويات هذه العمليات الآلية والاحتفاظ بها لمدة لا تقل عن 90 يوما.</li>
<li>يجب أن يوفر مزود الخدمة السحابية خدمة لإنشاء المفاتيح التشفيرية والتحكم فيها لتشفير بيانات المستخدم.</li>
<li>يجب أن يسمح مزود الخدمة السحابية للمستخدمين باستيراد مفاتيحهم الخاصة من أنظمة إدارة المفاتيح الخاصة بهم إلى خدمة إدارة المفاتيح السحابية.</li>
<li>يجب أن تتكامل خدمة إدارة المفاتيح الخاصة بمزود الخدمة السحابية مع خدمات سحابية أخرى لتوفير تشفير البيانات أثناء التخزين.</li>
<li>يجب أن يوفر مزود الخدمة السحابية وحدات أمان الأجهزة (HSM) المخصصة، والتي توفر تخزينا آمنًا للمفاتيح وعمليات تشفير مقاومة للعبث.</li>
<li>يجب أن يدعم مزود الخدمة السحابية استمرارية المفاتيح، بما في ذلك تخزين نسخ متعددة لضمان توفرها عند الحاجة.</li>
<li>يجب أن يوفر مزود الخدمة السحابية خدمة تسجيل دخول موحدة (SSO) مدارة، تتيح للمستخدمين إدارة الوصول إلى الحسابات والتطبيقات مركزيًا.</li>
<li>يجب أن يوفر مزود الخدمة السحابية خدمة مُدارة لإصدار وإدارة ونشر شهادات.SSL/TLS</li>
<li>يجب أن تسهل خدمة إدارة الشهادات الخاصة بمزود الخدمة السحابية عملية تجديد الشهادات.</li>
<li>يجب أن تدعم خدمة إدارة الشهادات الخاصة بمزود الخدمة السحابية استخدام Wildcard .شهادات</li>
<li>يجب أن تعمل خدمة إدارة الشهادات الخاصة بمزود الخدمة السحابية كهيئة تصديق. (CA)</li>
<li>يجب أن يوفر مزود الخدمة السحابية خدمة Microsoft Active Directory مدارة في السحابة.</li>
<li>يجب أن تدعم خدمة Microsoft Active Directory المُدارة في السحابة التكامل مع الدليل النشط المحلي. (On-Premises AD)</li>
<li>يجب أن تدعم خدمة Microsoft Active Directory المدارة بروتوكول LDAP</li>
<li>يجب أن تدعم خدمة Microsoft Active Directory المدارة بروتوكول.SAML</li>
<li>يجب أن يوفر مزود الخدمة السحابية خدمة مُدارة لتدوير وإدارة واسترجاع بيانات الاعتماد مثل مفاتيح API، وكلمات مرور قواعد البيانات، والأسرار الأخرى.</li>
<li>يجب أن يوفر مزود الخدمة السحابية جدار حماية لتطبيقات الويب (WAF) لحماية التطبيقات من الهجمات الشائعة التي تؤثر على التوافر والأمان.</li>
<li>يجب أن يوفر مزود الخدمة السحابية خدمة للحماية من هجمات DDoS على مستوى الشبكة والنقل، مع القدرة على كتابة قواعد مخصصة للتخفيف من الهجمات المتقدمة على مستوى التطبيق، بما في ذلك الطبقة 7.</li>
<li>يجب أن يوفر مزود الخدمة السحابية خدمة لتقييم الثغرات الأمنية في التطبيقات والموارد بشكل تلقائي.</li>
<li>يجب أن يوفر مزود الخدمة السحابية خدمة مُدارة لاكتشاف التهديدات الأمنية.</li>
<li>يجب أن يوفر مزود الخدمة السحابية خدمة مُدارة لتجميع نتائج الأمان وتمكين المستخدمين من عرض وإدارة التنبيهات الأمنية مركزيًا.</li>
<li>يجب أن يدعم مزود الخدمة السحابية إدارة الثغرات الأمنية من خلال عملية آلية تحافظ على أمان البيئة السحابية من الهجمات الإلكترونية وتسريبات البيانات.</li>
<li>يجب أن يجري مزود الخدمة السحابية عمليات مسح دورية للكشف عن الثغرات الأمنية.</li>
<li>يجب أن يسمح مزود الخدمة السحابية للجهات الخارجية بإجراء اختبارات الاختراق ومسح الثغرات الأمنية بشكل دوري على التطبيقات والشبكات الخاصة بالمستخدمين.</li>
<li>يجب أن يوفر مزود الخدمة السحابية أدوات لإدارة هوية المستخدمين والتحكم في الوصول إلى الموارد.</li>
<li>يجب أن يوفر مزود الخدمة السحابية حلاً لمنع فقدان البيانات (DLP) أو منع استخراج البيانات لجميع الأنظمة التي تتكامل مع خدمة السحابة.</li>
<li>يجب أن يكون لدى مزود الخدمة السحابية معايير أمان إلكتروني لكل مكون من مكونات بنيته التحتية، مثل المحاكيات الافتراضية، وأنظمة التشغيل، وأجهزة التوجيه، وخوادم DNS، وغيرها.</li>
<li>أن يقر مقدم الخدمة ويلتزم بعدم فرض أية رسوم أو تكاليف إضافية على الجامعة في حال الرغبة بالتحول إلى مزود خدمة آخر أو اتخاذ قرار بالعودة إلى المقر الرئيسي، على أن يتم ذلك دون قيد أو شرط وفي اي وقت.</li>
<li>يلتزم مقدم الخدمة بترحيل الرصيد غير المستخدم إلى السنوات التالية دون اشتراط حد أدنى للاستخدام السنوي.</li>
<li>مدة العقد (3) سنوات من تاريخ استلام الموقع.</li>
<li>المستخلصات:</li>
</ol>
<p>يتم دفع المستخلصات على دفعتين كالتالي:</p>
<h3>الدفعة الأولى :</h3>
<ul>
<li>يتم صرف قيمة البنود من ( 1 إلى 37 ) بعد 4 أشهر من تاريخ استلام الموقع.</li>
</ul>
<h3>الدفعة الثانية:</h3>
<ul>
<li>يتم صرف قيمة البند رقم (38) بعد 3 سنوات من تاريخ صرف الدفعة الأولى.</li>
</ul>
<ol start="71">
<li>الغرامات:</li>
</ol>
<p>يتم فرض غرامة سوء أداء على مزود الخدمة بنسبة لا تزيد عن (1% الى 20%) من قيمة المستخلص إذا لم يلتزم في تقديم الخدمات المطلوبة.</p>

    </body>
    </html>
    